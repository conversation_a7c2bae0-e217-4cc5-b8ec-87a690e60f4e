/**
 * 端到端加密模块
 * 使用 ECDH 密钥交换 + AES-256-GCM 加密
 */
class CryptoManager {
    constructor() {
        this.keyPair = null;
        this.sharedKey = null;
        this.peerPublicKey = null;
        this.isInitialized = false;
    }

    /**
     * 初始化加密管理器
     * 生成 ECDH 密钥对
     */
    async initialize() {
        try {
            console.log('[Crypto] 初始化加密管理器...');
            
            // 生成 ECDH 密钥对
            this.keyPair = await window.crypto.subtle.generateKey(
                {
                    name: 'ECDH',
                    namedCurve: 'P-256'
                },
                false, // 不可导出私钥
                ['deriveKey', 'deriveBits']
            );

            this.isInitialized = true;
            console.log('[Crypto] 加密管理器初始化完成');
            return true;
        } catch (error) {
            console.error('[Crypto] 初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取公钥（用于密钥交换）
     * @returns {Promise<ArrayBuffer>} 公钥数据
     */
    async getPublicKey() {
        if (!this.isInitialized) {
            throw new Error('加密管理器未初始化');
        }

        try {
            const publicKeyBuffer = await window.crypto.subtle.exportKey(
                'raw',
                this.keyPair.publicKey
            );
            return publicKeyBuffer;
        } catch (error) {
            console.error('[Crypto] 导出公钥失败:', error);
            throw error;
        }
    }

    /**
     * 设置对方的公钥并生成共享密钥
     * @param {ArrayBuffer} peerPublicKeyBuffer 对方的公钥
     */
    async setPeerPublicKey(peerPublicKeyBuffer) {
        if (!this.isInitialized) {
            throw new Error('加密管理器未初始化');
        }

        try {
            console.log('[Crypto] 设置对方公钥，长度:', peerPublicKeyBuffer.byteLength);

            // 导入对方的公钥
            this.peerPublicKey = await window.crypto.subtle.importKey(
                'raw',
                peerPublicKeyBuffer,
                {
                    name: 'ECDH',
                    namedCurve: 'P-256'
                },
                false,
                []
            );

            console.log('[Crypto] 对方公钥导入成功');

            // 生成共享密钥
            this.sharedKey = await window.crypto.subtle.deriveKey(
                {
                    name: 'ECDH',
                    public: this.peerPublicKey
                },
                this.keyPair.privateKey,
                {
                    name: 'AES-GCM',
                    length: 256
                },
                false,
                ['encrypt', 'decrypt']
            );

            console.log('[Crypto] 共享密钥生成完成');
            return true;
        } catch (error) {
            console.error('[Crypto] 设置对方公钥失败:', error);
            console.error('[Crypto] 错误详情:', error.name, error.message);
            return false;
        }
    }

    /**
     * 加密消息
     * @param {string} message 要加密的消息
     * @returns {Promise<Object>} 包含加密数据和IV的对象
     */
    async encryptMessage(message) {
        if (!this.sharedKey) {
            throw new Error('共享密钥未生成');
        }

        try {
            // 生成随机IV
            const iv = window.crypto.getRandomValues(new Uint8Array(12));
            
            // 将消息转换为ArrayBuffer
            const messageBuffer = new TextEncoder().encode(message);
            
            // 加密消息
            const encryptedBuffer = await window.crypto.subtle.encrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                this.sharedKey,
                messageBuffer
            );

            return {
                encrypted: encryptedBuffer,
                iv: iv
            };
        } catch (error) {
            console.error('[Crypto] 加密失败:', error);
            throw error;
        }
    }

    /**
     * 解密消息
     * @param {ArrayBuffer} encryptedData 加密的数据
     * @param {Uint8Array} iv 初始化向量
     * @returns {Promise<string>} 解密后的消息
     */
    async decryptMessage(encryptedData, iv) {
        if (!this.sharedKey) {
            throw new Error('共享密钥未生成');
        }

        try {
            // 解密消息
            const decryptedBuffer = await window.crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                this.sharedKey,
                encryptedData
            );

            // 将ArrayBuffer转换为字符串
            const decryptedMessage = new TextDecoder().decode(decryptedBuffer);
            return decryptedMessage;
        } catch (error) {
            console.error('[Crypto] 解密失败:', error);
            throw error;
        }
    }

    /**
     * 检查是否可以进行加密通信
     * @returns {boolean} 是否已准备好加密通信
     */
    isReady() {
        return this.isInitialized && this.sharedKey !== null;
    }

    /**
     * 重置加密管理器
     */
    reset() {
        this.keyPair = null;
        this.sharedKey = null;
        this.peerPublicKey = null;
        this.isInitialized = false;
        console.log('[Crypto] 加密管理器已重置');
    }
}

/**
 * 工具函数：ArrayBuffer 转 Base64
 * @param {ArrayBuffer} buffer 
 * @returns {string} Base64字符串
 */
function arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
}

/**
 * 工具函数：Base64 转 ArrayBuffer
 * @param {string} base64 Base64字符串
 * @returns {ArrayBuffer} ArrayBuffer
 */
function base64ToArrayBuffer(base64) {
    const binary = window.atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
}

/**
 * 工具函数：Uint8Array 转 Base64
 * @param {Uint8Array} uint8Array 
 * @returns {string} Base64字符串
 */
function uint8ArrayToBase64(uint8Array) {
    let binary = '';
    for (let i = 0; i < uint8Array.byteLength; i++) {
        binary += String.fromCharCode(uint8Array[i]);
    }
    return window.btoa(binary);
}

/**
 * 工具函数：Base64 转 Uint8Array
 * @param {string} base64 Base64字符串
 * @returns {Uint8Array} Uint8Array
 */
function base64ToUint8Array(base64) {
    const binary = window.atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
}
