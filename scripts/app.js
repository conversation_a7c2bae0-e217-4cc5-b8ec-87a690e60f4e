/**
 * 主应用程序
 * 协调加密、通信和用户界面
 */
class ChatApp {
    constructor() {
        this.cryptoManager = new CryptoManager();
        this.wsManager = new WebSocketManager();
        this.currentRoomId = null;
        this.peerCount = 0;
        this.isKeyExchanged = false;
        
        this.initializeElements();
        this.bindEvents();
        this.initializeApp();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 状态元素
        this.connectionStatus = document.getElementById('connectionStatus');
        this.encryptionStatus = document.getElementById('encryptionStatus');
        this.peerCountElement = document.getElementById('peerCount');
        
        // 房间设置元素
        this.roomSetup = document.getElementById('roomSetup');
        this.roomIdInput = document.getElementById('roomId');
        this.joinBtn = document.getElementById('joinBtn');
        this.roomInfo = document.getElementById('roomInfo');
        this.roomIdDisplay = document.getElementById('roomIdDisplay');
        
        // 聊天区域元素
        this.chatArea = document.getElementById('chatArea');
        this.messages = document.getElementById('messages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 加入房间按钮
        this.joinBtn.addEventListener('click', () => this.joinRoom());
        
        // 回车键加入房间
        this.roomIdInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.joinRoom();
            }
        });
        
        // 发送消息按钮
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 消息输入框事件
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.sendMessage();
            }
        });
        
        // 自动调整输入框高度
        this.messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight();
        });
        
        // WebSocket事件
        this.wsManager.on('connected', () => this.onConnected());
        this.wsManager.on('disconnected', () => this.onDisconnected());
        this.wsManager.on('room_joined', (data) => this.onRoomJoined(data));
        this.wsManager.on('peer_joined', (data) => this.onPeerJoined(data));
        this.wsManager.on('peer_left', (data) => this.onPeerLeft(data));
        this.wsManager.on('key_exchange', (data) => this.onKeyExchange(data));
        this.wsManager.on('encrypted_message', (data) => this.onEncryptedMessage(data));
        this.wsManager.on('room_info', (data) => this.onRoomInfo(data));
        this.wsManager.on('error', (error) => this.onError(error));
    }

    /**
     * 初始化应用程序
     */
    async initializeApp() {
        console.log('[App] 初始化应用程序...');
        
        try {
            // 初始化加密管理器
            const cryptoInitialized = await this.cryptoManager.initialize();
            if (!cryptoInitialized) {
                throw new Error('加密模块初始化失败');
            }
            
            console.log('[App] 应用程序初始化完成');
        } catch (error) {
            console.error('[App] 初始化失败:', error);
            this.showError('应用程序初始化失败，请刷新页面重试');
        }
    }

    /**
     * 加入房间
     */
    async joinRoom() {
        const roomId = this.roomIdInput.value.trim() || this.generateRoomId();
        
        if (!roomId) {
            this.showError('房间ID不能为空');
            return;
        }
        
        console.log(`[App] 加入房间: ${roomId}`);
        this.currentRoomId = roomId;
        
        // 连接WebSocket
        this.wsManager.connect('ws://localhost:8080', roomId);
        
        // 显示房间信息
        this.roomIdDisplay.textContent = roomId;
        this.roomInfo.style.display = 'block';
        
        // 禁用输入
        this.roomIdInput.disabled = true;
        this.joinBtn.disabled = true;
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const message = this.messageInput.value.trim();

        if (!message) {
            return;
        }

        if (!this.cryptoManager.isReady()) {
            this.showError('加密未就绪，请等待密钥交换完成');
            return;
        }

        try {
            console.log('[App] 开始加密消息:', message);

            // 加密消息
            const { encrypted, iv } = await this.cryptoManager.encryptMessage(message);

            console.log('[App] 消息加密成功，数据长度:', encrypted.byteLength, 'IV长度:', iv.byteLength);

            // 转换为Base64
            const encryptedBase64 = arrayBufferToBase64(encrypted);
            const ivBase64 = uint8ArrayToBase64(iv);

            // 发送加密消息
            const sent = this.wsManager.sendEncryptedMessage(encryptedBase64, ivBase64);

            if (sent) {
                console.log('[App] 加密消息发送成功');
                // 显示自己的消息
                this.addMessage(message, true);
                this.messageInput.value = '';
                this.adjustTextareaHeight();
            } else {
                console.error('[App] 消息发送失败');
                this.showError('消息发送失败');
            }

        } catch (error) {
            console.error('[App] 发送消息失败:', error);
            this.showError('发送消息失败');
        }
    }

    /**
     * WebSocket连接成功
     */
    onConnected() {
        console.log('[App] WebSocket连接成功');
        this.updateConnectionStatus(true);
    }

    /**
     * WebSocket连接断开
     */
    onDisconnected() {
        console.log('[App] WebSocket连接断开');
        this.updateConnectionStatus(false);
        this.updateEncryptionStatus(false);
    }

    /**
     * 成功加入房间
     */
    onRoomJoined(data) {
        console.log('[App] 成功加入房间');
        this.showChatArea();
    }

    /**
     * 有新用户加入
     */
    async onPeerJoined(data) {
        console.log('[App] 新用户加入');
        this.peerCount++;
        this.updatePeerCount();

        // 发送公钥进行密钥交换（只有在还没有交换过密钥时才发送）
        if (!this.isKeyExchanged) {
            try {
                const publicKey = await this.cryptoManager.getPublicKey();
                const publicKeyBase64 = arrayBufferToBase64(publicKey);
                this.wsManager.sendKeyExchange(publicKeyBase64);
                console.log('[App] 发送公钥给新用户');
            } catch (error) {
                console.error('[App] 发送公钥失败:', error);
            }
        }
    }

    /**
     * 用户离开
     */
    onPeerLeft(data) {
        console.log('[App] 用户离开');
        this.peerCount = Math.max(0, this.peerCount - 1);
        this.updatePeerCount();
        
        // 如果没有其他用户，重置加密状态
        if (this.peerCount === 0) {
            this.isKeyExchanged = false;
            this.updateEncryptionStatus(false);
        }
    }

    /**
     * 收到密钥交换
     */
    async onKeyExchange(data) {
        console.log('[App] 收到密钥交换');

        try {
            // 设置对方公钥
            const peerPublicKey = base64ToArrayBuffer(data.publicKey);
            const success = await this.cryptoManager.setPeerPublicKey(peerPublicKey);

            if (success) {
                console.log('[App] 密钥交换成功，加密已就绪');
                this.isKeyExchanged = true;
                this.updateEncryptionStatus(true);

                // 如果还没有发送过自己的公钥，现在发送
                const publicKey = await this.cryptoManager.getPublicKey();
                const publicKeyBase64 = arrayBufferToBase64(publicKey);
                this.wsManager.sendKeyExchange(publicKeyBase64);
                console.log('[App] 回复公钥给对方');
            }
        } catch (error) {
            console.error('[App] 密钥交换失败:', error);
            this.showError('密钥交换失败');
        }
    }

    /**
     * 收到加密消息
     */
    async onEncryptedMessage(data) {
        console.log('[App] 收到加密消息');

        // 检查加密是否就绪
        if (!this.cryptoManager.isReady()) {
            console.warn('[App] 加密未就绪，无法解密消息');
            this.addMessage('[加密未就绪，无法解密]', false);
            return;
        }

        try {
            // 解密消息
            const encryptedData = base64ToArrayBuffer(data.encryptedData);
            const iv = base64ToUint8Array(data.iv);

            console.log('[App] 开始解密消息，数据长度:', encryptedData.byteLength, 'IV长度:', iv.byteLength);

            const decryptedMessage = await this.cryptoManager.decryptMessage(encryptedData, iv);

            console.log('[App] 消息解密成功:', decryptedMessage);

            // 显示消息
            this.addMessage(decryptedMessage, false);

        } catch (error) {
            console.error('[App] 解密消息失败:', error);
            console.error('[App] 错误详情:', error.name, error.message);
            this.addMessage('[解密失败的消息]', false);
        }
    }

    /**
     * 收到房间信息
     */
    onRoomInfo(data) {
        console.log('[App] 收到房间信息');
        this.peerCount = data.peerCount || 0;
        this.updatePeerCount();
    }

    /**
     * 处理错误
     */
    onError(error) {
        console.error('[App] WebSocket错误:', error);
        this.showError('连接错误，请检查网络或刷新页面重试');
    }

    /**
     * 显示聊天区域
     */
    showChatArea() {
        this.roomSetup.style.display = 'none';
        this.chatArea.style.display = 'grid';
        this.messageInput.focus();
    }

    /**
     * 添加消息到聊天区域
     */
    addMessage(text, isOwn) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isOwn ? 'own' : 'other'}`;
        
        const messageText = document.createElement('div');
        messageText.textContent = text;
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = new Date().toLocaleTimeString();
        
        messageDiv.appendChild(messageText);
        messageDiv.appendChild(messageTime);
        
        this.messages.appendChild(messageDiv);
        this.messages.scrollTop = this.messages.scrollHeight;
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(connected) {
        this.connectionStatus.textContent = connected ? 'CONNECTED' : 'DISCONNECTED';
        this.connectionStatus.className = `connection-status ${connected ? 'connected' : ''}`;
    }

    /**
     * 更新加密状态
     */
    updateEncryptionStatus(encrypted) {
        this.encryptionStatus.textContent = encrypted ? 'ENCRYPTED' : 'NO ENCRYPTION';
        this.encryptionStatus.className = `encryption-status ${encrypted ? 'encrypted' : ''}`;
    }

    /**
     * 更新用户数量
     */
    updatePeerCount() {
        this.peerCountElement.textContent = this.peerCount.toString();
    }

    /**
     * 调整文本框高度
     */
    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    /**
     * 生成随机房间ID
     */
    generateRoomId() {
        return Math.random().toString(36).substring(2, 10).toUpperCase();
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 简单的错误显示，可以后续改进
        alert(message);
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    console.log('[App] DOM加载完成，启动应用程序');
    window.chatApp = new ChatApp();
});
