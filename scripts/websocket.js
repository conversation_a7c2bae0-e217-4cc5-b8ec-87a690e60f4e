/**
 * WebSocket 通信管理器
 * 处理实时消息传输和连接管理
 */
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.roomId = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.eventHandlers = {};
    }

    /**
     * 连接到WebSocket服务器
     * @param {string} serverUrl WebSocket服务器地址
     * @param {string} roomId 房间ID
     */
    connect(serverUrl = 'ws://localhost:8080', roomId) {
        try {
            console.log(`[WebSocket] 连接到服务器: ${serverUrl}, 房间: ${roomId}`);
            
            this.roomId = roomId;
            this.socket = new WebSocket(serverUrl);
            
            // 连接打开事件
            this.socket.onopen = (event) => {
                console.log('[WebSocket] 连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // 加入房间
                this.send({
                    type: 'join_room',
                    roomId: this.roomId,
                    timestamp: Date.now()
                });
                
                this.emit('connected', event);
            };

            // 接收消息事件
            this.socket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('[WebSocket] 收到消息:', data.type);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('[WebSocket] 解析消息失败:', error);
                }
            };

            // 连接关闭事件
            this.socket.onclose = (event) => {
                console.log('[WebSocket] 连接已关闭:', event.code, event.reason);
                this.isConnected = false;
                this.emit('disconnected', event);
                
                // 尝试重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.attemptReconnect();
                }
            };

            // 连接错误事件
            this.socket.onerror = (error) => {
                console.error('[WebSocket] 连接错误:', error);
                this.emit('error', error);
            };

        } catch (error) {
            console.error('[WebSocket] 连接失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 处理接收到的消息
     * @param {Object} data 消息数据
     */
    handleMessage(data) {
        switch (data.type) {
            case 'room_joined':
                console.log('[WebSocket] 成功加入房间');
                this.emit('room_joined', data);
                break;
                
            case 'peer_joined':
                console.log('[WebSocket] 有新用户加入');
                this.emit('peer_joined', data);
                break;
                
            case 'peer_left':
                console.log('[WebSocket] 用户离开');
                this.emit('peer_left', data);
                break;
                
            case 'key_exchange':
                console.log('[WebSocket] 收到密钥交换');
                this.emit('key_exchange', data);
                break;
                
            case 'encrypted_message':
                console.log('[WebSocket] 收到加密消息');
                this.emit('encrypted_message', data);
                break;
                
            case 'room_info':
                console.log('[WebSocket] 收到房间信息');
                this.emit('room_info', data);
                break;
                
            case 'error':
                console.error('[WebSocket] 服务器错误:', data.message);
                this.emit('server_error', data);
                break;
                
            default:
                console.warn('[WebSocket] 未知消息类型:', data.type);
        }
    }

    /**
     * 发送消息到服务器
     * @param {Object} data 要发送的数据
     */
    send(data) {
        if (!this.isConnected || !this.socket) {
            console.error('[WebSocket] 连接未建立，无法发送消息');
            return false;
        }

        try {
            const message = JSON.stringify(data);
            this.socket.send(message);
            console.log('[WebSocket] 发送消息:', data.type);
            return true;
        } catch (error) {
            console.error('[WebSocket] 发送消息失败:', error);
            return false;
        }
    }

    /**
     * 发送密钥交换消息
     * @param {string} publicKeyBase64 Base64编码的公钥
     */
    sendKeyExchange(publicKeyBase64) {
        return this.send({
            type: 'key_exchange',
            roomId: this.roomId,
            publicKey: publicKeyBase64,
            timestamp: Date.now()
        });
    }

    /**
     * 发送加密消息
     * @param {string} encryptedDataBase64 Base64编码的加密数据
     * @param {string} ivBase64 Base64编码的IV
     */
    sendEncryptedMessage(encryptedDataBase64, ivBase64) {
        return this.send({
            type: 'encrypted_message',
            roomId: this.roomId,
            encryptedData: encryptedDataBase64,
            iv: ivBase64,
            timestamp: Date.now()
        });
    }

    /**
     * 尝试重连
     */
    attemptReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`[WebSocket] ${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
        
        setTimeout(() => {
            if (!this.isConnected && this.roomId) {
                this.connect('ws://localhost:8080', this.roomId);
            }
        }, delay);
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            console.log('[WebSocket] 主动断开连接');
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
        this.roomId = null;
        this.reconnectAttempts = 0;
    }

    /**
     * 注册事件处理器
     * @param {string} event 事件名称
     * @param {Function} handler 处理函数
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }

    /**
     * 移除事件处理器
     * @param {string} event 事件名称
     * @param {Function} handler 处理函数
     */
    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event 事件名称
     * @param {*} data 事件数据
     */
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`[WebSocket] 事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取连接状态
     * @returns {boolean} 是否已连接
     */
    getConnectionStatus() {
        return this.isConnected;
    }

    /**
     * 获取当前房间ID
     * @returns {string|null} 房间ID
     */
    getRoomId() {
        return this.roomId;
    }
}
