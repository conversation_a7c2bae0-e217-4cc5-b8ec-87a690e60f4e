# 端到端加密聊天网站 - 使用说明

## 🚀 快速开始

### 1. 启动服务器
```bash
cd server
npm install
npm start
```
服务器将在 `http://localhost:8080` 启动

### 2. 打开聊天页面
在浏览器中打开 `index.html` 文件，或者使用本地服务器：
```bash
python3 -m http.server 3000
# 然后访问 http://localhost:3000
```

### 3. 开始聊天
1. 输入房间ID（或留空自动生成）
2. 点击 "JOIN ROOM" 按钮
3. 分享房间ID给其他人
4. 等待对方加入并完成密钥交换
5. 开始安全聊天！

## 🔒 安全特性

### 端到端加密
- **ECDH P-256密钥交换**：安全的密钥协商协议
- **AES-256-GCM加密**：军用级别的对称加密
- **随机IV**：每条消息使用不同的初始化向量
- **前向安全**：每次会话生成新的临时密钥

### 隐私保护
- **无注册系统**：无需提供个人信息
- **本地加密**：所有加密操作在浏览器本地完成
- **服务器盲传**：服务器无法解密消息内容
- **内存存储**：密钥仅存在于浏览器内存中

## 🎨 界面特色

### 极简黑白设计
- **纯粹配色**：只使用黑白两色
- **等宽字体**：SF Mono字体增强技术感
- **线条美学**：通过边框创造结构感
- **留白艺术**：适当留白避免视觉拥挤
- **网格布局**：严格的网格系统保证秩序感

### 状态指示
- **连接状态**：实时显示WebSocket连接状态
- **加密状态**：显示端到端加密是否就绪
- **用户数量**：显示当前房间的用户数量

## 📱 使用技巧

### 房间管理
- **房间ID**：支持字母数字组合，建议8-12位
- **多房间**：不同房间完全隔离，互不干扰
- **自动清理**：空房间会自动删除

### 消息发送
- **快捷键**：Ctrl+Enter 快速发送消息
- **自动调整**：输入框高度自动调整
- **实时传输**：消息实时加密传输

### 连接管理
- **自动重连**：网络断开时自动重连
- **心跳检测**：定期检测连接状态
- **超时处理**：自动清理超时连接

## 🛠️ 故障排除

### 连接问题
**问题**：显示 "DISCONNECTED" 状态
**解决**：
1. 检查服务器是否启动：`npm start`
2. 检查端口是否被占用：`lsof -i :8080`
3. 检查防火墙设置

**问题**：无法加入房间
**解决**：
1. 检查房间ID格式（不超过20字符）
2. 刷新页面重试
3. 检查浏览器控制台错误信息

### 加密问题
**问题**：显示 "NO ENCRYPTION" 状态
**解决**：
1. 确保至少有2个用户在房间中
2. 等待密钥交换完成（通常几秒钟）
3. 检查浏览器是否支持Web Crypto API

**问题**：消息显示 "[解密失败的消息]"
**解决**：
1. 确保发送方和接收方都完成了密钥交换
2. 检查网络连接是否稳定
3. 刷新页面重新建立连接

### 性能问题
**问题**：页面响应缓慢
**解决**：
1. 检查服务器资源使用：`./monitor.sh`
2. 清理浏览器缓存
3. 关闭其他占用资源的标签页

## 🔧 高级配置

### 服务器配置
可以通过环境变量调整服务器参数：
```bash
export PORT=8080                    # 服务器端口
export MAX_ROOMS=1000               # 最大房间数
export MAX_CLIENTS_PER_ROOM=10      # 每房间最大人数
export CLIENT_TIMEOUT=600000        # 客户端超时时间(毫秒)
export HEARTBEAT_INTERVAL=30000     # 心跳间隔(毫秒)
export MAX_MESSAGE_SIZE=65536       # 最大消息大小(字节)
```

### 浏览器兼容性
- **Chrome/Edge**: 完全支持
- **Firefox**: 完全支持
- **Safari**: 完全支持
- **移动浏览器**: 基本支持

### 网络要求
- **WebSocket支持**：现代浏览器都支持
- **Web Crypto API**：HTTPS环境下或localhost可用
- **带宽要求**：极低，每条消息仅几KB

## 📊 性能指标

### 资源使用
- **服务器内存**：约40MB基础占用
- **客户端内存**：约10MB每个标签页
- **网络流量**：每条消息约1-2KB
- **CPU使用**：加密解密时短暂占用

### 并发能力
- **默认配置**：支持1000个房间，每房间10人
- **低配服务器**：建议500个房间，每房间8人
- **高配服务器**：可调整至更高限制

## 🚨 安全注意事项

### 使用建议
1. **HTTPS部署**：生产环境必须使用HTTPS
2. **定期更新**：及时更新依赖包
3. **监控日志**：定期检查服务器日志
4. **备份配置**：备份重要配置文件

### 限制说明
1. **不适合文件传输**：仅支持文本消息
2. **不支持群聊**：目前仅支持两人聊天
3. **无消息历史**：关闭页面后消息不保存
4. **依赖网络**：需要稳定的网络连接

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查服务器日志：`pm2 logs encrypted-chat`
3. 运行测试：`npm test`
4. 查看系统状态：`./monitor.sh`

---

**记住**：这是一个端到端加密的聊天系统，你的隐私和安全是我们的首要考虑！
