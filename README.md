# 端到端加密聊天网站

一个极简的端到端加密聊天应用，遵循纯粹的黑白视觉哲学。

## 项目特点

### 安全特性
- **端到端加密**：使用AES-256-GCM加密算法
- **密钥交换**：基于ECDH密钥交换协议
- **前向安全**：每次会话生成新的临时密钥
- **无注册系统**：无需存储用户信息，保护隐私

### 视觉设计
- **纯粹黑白**：拒绝一切彩色元素，坚持黑白两色的纯净表达
- **线条美学**：通过边框和分割线创造结构感
- **留白艺术**：适当的留白创造呼吸感
- **等宽字体**：使用SF Mono等等宽字体，增强技术感
- **网格布局**：严格的网格系统，保证界面秩序感

## 技术架构

### 前端
- **HTML5**：语义化结构
- **CSS3**：纯CSS实现，无框架依赖
- **JavaScript**：原生JS，Web Crypto API
- **WebSocket**：实时通信

### 后端（待开发）
- **Node.js**：服务器运行环境
- **WebSocket**：实时消息传输
- **内存存储**：临时消息中转，不持久化

## 加密流程

1. **密钥生成**：每个用户生成ECDH密钥对
2. **密钥交换**：通过服务器安全交换公钥
3. **共享密钥**：计算ECDH共享密钥
4. **消息加密**：使用AES-256-GCM加密消息
5. **安全传输**：加密消息通过WebSocket传输
6. **本地解密**：接收方本地解密消息

## 文件结构

```
/
├── README.md           # 项目说明
├── index.html         # 主页面
├── styles/
│   └── main.css       # 主样式文件
├── scripts/
│   ├── crypto.js      # 加密模块
│   ├── websocket.js   # WebSocket通信
│   └── app.js         # 主应用逻辑
└── server/            # 后端代码（待开发）
    └── server.js      # 服务器主文件
```

## 使用方法

1. 打开网站
2. 输入房间ID或生成新房间
3. 分享房间ID给对方
4. 开始安全聊天

## 安全说明

- 所有加密操作在客户端完成
- 服务器仅作为消息中转，无法解密内容
- 密钥仅存在于用户浏览器内存中
- 关闭页面后所有密钥自动销毁

## 开发状态

- [x] 项目规划
- [x] 前端界面开发
- [x] 加密模块实现
- [x] WebSocket通信
- [ ] 后端服务器
- [ ] 测试与优化

## 已完成功能

### 前端界面
- ✅ 极简黑白设计风格
- ✅ 响应式布局
- ✅ 房间加入界面
- ✅ 聊天消息界面
- ✅ 状态指示器（连接状态、加密状态、用户数量）
- ✅ 等宽字体和网格布局

### 加密系统
- ✅ ECDH密钥交换协议
- ✅ AES-256-GCM消息加密
- ✅ 随机IV生成
- ✅ 前向安全性
- ✅ 客户端加密/解密

### 通信系统
- ✅ WebSocket实时通信
- ✅ 自动重连机制
- ✅ 消息类型处理
- ✅ 错误处理和日志

## 技术实现细节

### 加密流程
1. 每个客户端生成ECDH P-256密钥对
2. 通过WebSocket交换公钥
3. 计算共享密钥用于AES-256-GCM加密
4. 每条消息使用随机IV加密
5. 服务器仅转发加密数据，无法解密

### 安全特性
- 端到端加密，服务器无法读取消息内容
- 使用Web Crypto API，确保加密操作安全
- 密钥仅存在于浏览器内存，关闭页面自动销毁
- 每次会话生成新的临时密钥对
