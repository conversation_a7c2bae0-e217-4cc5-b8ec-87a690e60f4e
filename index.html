<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端到端加密聊天</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1 class="title">ENCRYPTED CHAT</h1>
            <div class="status-bar">
                <span class="connection-status" id="connectionStatus">DISCONNECTED</span>
                <span class="encryption-status" id="encryptionStatus">NO ENCRYPTION</span>
            </div>
        </header>

        <!-- 房间设置区域 -->
        <section class="room-setup" id="roomSetup">
            <div class="input-group">
                <label for="roomId" class="label">ROOM ID</label>
                <div class="input-container">
                    <input type="text" id="roomId" class="input" placeholder="输入房间ID或留空自动生成">
                    <button class="btn btn-primary" id="joinBtn">JOIN ROOM</button>
                </div>
            </div>
            <div class="room-info" id="roomInfo" style="display: none;">
                <p class="info-text">房间ID: <span class="room-id-display" id="roomIdDisplay"></span></p>
                <p class="info-text">分享此ID给对方加入聊天</p>
            </div>
        </section>

        <!-- 聊天区域 -->
        <main class="chat-area" id="chatArea" style="display: none;">
            <!-- 消息列表 -->
            <div class="messages-container">
                <div class="messages" id="messages">
                    <!-- 消息将在这里动态添加 -->
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <div class="message-input-container">
                    <textarea 
                        id="messageInput" 
                        class="message-input" 
                        placeholder="输入消息... (Ctrl+Enter发送)"
                        rows="1"
                    ></textarea>
                    <button class="btn btn-send" id="sendBtn">SEND</button>
                </div>
            </div>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <div class="encryption-info">
                <span class="info-label">ENCRYPTION:</span>
                <span class="info-value" id="encryptionInfo">AES-256-GCM + ECDH</span>
            </div>
            <div class="peer-info">
                <span class="info-label">PEERS:</span>
                <span class="info-value" id="peerCount">0</span>
            </div>
        </footer>
    </div>

    <!-- 加载脚本 -->
    <script src="scripts/crypto.js"></script>
    <script src="scripts/websocket.js"></script>
    <script src="scripts/app.js"></script>
</body>
</html>
