/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量 */
:root {
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-light: #f5f5f5;
    --color-gray-medium: #cccccc;
    --color-gray-dark: #333333;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 20px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --border-width: 1px;
    --border-radius: 0;
}

/* 基础样式 */
body {
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-black);
    background-color: var(--color-white);
    overflow-x: hidden;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: grid;
    grid-template-rows: auto auto 1fr auto;
    border-left: var(--border-width) solid var(--color-black);
    border-right: var(--border-width) solid var(--color-black);
}

/* 头部 */
.header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--color-black);
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: var(--spacing-lg);
}

.title {
    font-size: var(--font-size-xl);
    font-weight: normal;
    letter-spacing: 2px;
}

.status-bar {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-xs);
    letter-spacing: 1px;
}

.connection-status,
.encryption-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: var(--border-width) solid var(--color-black);
    background-color: var(--color-white);
}

.connection-status.connected {
    background-color: var(--color-black);
    color: var(--color-white);
}

.encryption-status.encrypted {
    background-color: var(--color-black);
    color: var(--color-white);
}

/* 房间设置 */
.room-setup {
    padding: var(--spacing-xl);
    border-bottom: var(--border-width) solid var(--color-black);
}

.input-group {
    margin-bottom: var(--spacing-lg);
}

.label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    letter-spacing: 1px;
}

.input-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-md);
}

.input {
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--color-black);
    background-color: var(--color-white);
    outline: none;
}

.input:focus {
    background-color: var(--color-gray-light);
}

/* 按钮 */
.btn {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: var(--border-width) solid var(--color-black);
    background-color: var(--color-white);
    color: var(--color-black);
    cursor: pointer;
    letter-spacing: 1px;
    transition: all 0.2s ease;
}

.btn:hover {
    background-color: var(--color-black);
    color: var(--color-white);
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--color-black);
    color: var(--color-white);
}

.btn-primary:hover {
    background-color: var(--color-white);
    color: var(--color-black);
}

/* 房间信息 */
.room-info {
    padding: var(--spacing-lg);
    border: var(--border-width) solid var(--color-black);
    background-color: var(--color-gray-light);
    margin-top: var(--spacing-lg);
}

.info-text {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.room-id-display {
    font-weight: bold;
    letter-spacing: 2px;
}

/* 聊天区域 */
.chat-area {
    display: grid;
    grid-template-rows: 1fr auto;
    min-height: 0;
}

.messages-container {
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.messages {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 消息样式 */
.message {
    max-width: 70%;
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--color-black);
    word-wrap: break-word;
}

.message.own {
    align-self: flex-end;
    background-color: var(--color-black);
    color: var(--color-white);
}

.message.other {
    align-self: flex-start;
    background-color: var(--color-white);
}

.message-time {
    font-size: var(--font-size-xs);
    opacity: 0.7;
    margin-top: var(--spacing-xs);
}

/* 输入区域 */
.input-area {
    padding: var(--spacing-lg);
    border-top: var(--border-width) solid var(--color-black);
}

.message-input-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-md);
    align-items: end;
}

.message-input {
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--color-black);
    background-color: var(--color-white);
    resize: none;
    outline: none;
    min-height: 48px;
    max-height: 120px;
}

.message-input:focus {
    background-color: var(--color-gray-light);
}

.btn-send {
    height: 48px;
}

/* 底部信息 */
.footer {
    padding: var(--spacing-lg);
    border-top: var(--border-width) solid var(--color-black);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    font-size: var(--font-size-xs);
}

.info-label {
    letter-spacing: 1px;
}

.info-value {
    font-weight: bold;
    margin-left: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        border-left: none;
        border-right: none;
    }
    
    .header {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .status-bar {
        justify-self: start;
    }
    
    .input-container {
        grid-template-columns: 1fr;
    }
    
    .message {
        max-width: 85%;
    }
    
    .footer {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
    width: var(--spacing-sm);
}

.messages-container::-webkit-scrollbar-track {
    background: var(--color-white);
    border: var(--border-width) solid var(--color-black);
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--color-black);
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-dark);
}
