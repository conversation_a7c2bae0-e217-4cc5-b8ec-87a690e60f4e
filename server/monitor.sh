#!/bin/bash

# 服务器性能监控脚本
# 专为低性能Ubuntu服务器设计

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置
APP_NAME="encrypted-chat"
LOG_FILE="/var/log/encrypted-chat/monitor.log"
ALERT_MEMORY_THRESHOLD=80  # 内存使用率警告阈值
ALERT_CPU_THRESHOLD=90     # CPU使用率警告阈值

# 获取系统信息
get_system_info() {
    echo -e "${BLUE}=== 系统信息 ===${NC}"
    echo "主机名: $(hostname)"
    echo "系统: $(lsb_release -d | cut -f2)"
    echo "内核: $(uname -r)"
    echo "架构: $(uname -m)"
    echo "运行时间: $(uptime -p)"
    echo ""
}

# 获取资源使用情况
get_resource_usage() {
    echo -e "${BLUE}=== 资源使用情况 ===${NC}"
    
    # CPU使用率
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo -n "CPU使用率: "
    if (( $(echo "$CPU_USAGE > $ALERT_CPU_THRESHOLD" | bc -l) )); then
        echo -e "${RED}${CPU_USAGE}%${NC} (警告)"
    else
        echo -e "${GREEN}${CPU_USAGE}%${NC}"
    fi
    
    # 内存使用情况
    MEMORY_INFO=$(free -m | awk 'NR==2{printf "%.1f %.1f %.1f", $3*100/$2, $3, $2}')
    MEMORY_PERCENT=$(echo $MEMORY_INFO | awk '{print $1}')
    MEMORY_USED=$(echo $MEMORY_INFO | awk '{print $2}')
    MEMORY_TOTAL=$(echo $MEMORY_INFO | awk '{print $3}')
    
    echo -n "内存使用率: "
    if (( $(echo "$MEMORY_PERCENT > $ALERT_MEMORY_THRESHOLD" | bc -l) )); then
        echo -e "${RED}${MEMORY_PERCENT}%${NC} (${MEMORY_USED}MB/${MEMORY_TOTAL}MB) (警告)"
    else
        echo -e "${GREEN}${MEMORY_PERCENT}%${NC} (${MEMORY_USED}MB/${MEMORY_TOTAL}MB)"
    fi
    
    # 磁盘使用情况
    echo "磁盘使用率:"
    df -h | grep -E '^/dev/' | awk '{
        use = $5
        gsub(/%/, "", use)
        if (use > 80) 
            printf "  %s: \033[0;31m%s\033[0m (%s/%s)\n", $6, $5, $3, $2
        else 
            printf "  %s: \033[0;32m%s\033[0m (%s/%s)\n", $6, $5, $3, $2
    }'
    
    # 负载平均值
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}')
    echo "负载平均值:$LOAD_AVG"
    echo ""
}

# 获取网络连接情况
get_network_info() {
    echo -e "${BLUE}=== 网络连接情况 ===${NC}"
    
    # 活跃连接数
    ACTIVE_CONNECTIONS=$(netstat -an | grep :8080 | grep ESTABLISHED | wc -l)
    echo "WebSocket活跃连接: $ACTIVE_CONNECTIONS"
    
    # 监听端口
    echo "监听端口:"
    netstat -tlnp | grep -E ':(80|8080|443)' | awk '{print "  " $1 " " $4 " " $7}'
    
    # 网络流量（如果有ifstat）
    if command -v ifstat &> /dev/null; then
        echo "网络流量 (1秒采样):"
        ifstat -i eth0 1 1 | tail -n 1 | awk '{printf "  入站: %s KB/s, 出站: %s KB/s\n", $1, $2}'
    fi
    echo ""
}

# 获取应用状态
get_app_status() {
    echo -e "${BLUE}=== 应用状态 ===${NC}"
    
    # PM2状态
    if command -v pm2 &> /dev/null; then
        echo "PM2进程状态:"
        pm2 jlist | jq -r '.[] | select(.name=="'$APP_NAME'") | "  名称: \(.name), 状态: \(.pm2_env.status), PID: \(.pid), 内存: \(.memory/1024/1024|floor)MB, CPU: \(.cpu)%"' 2>/dev/null || echo "  PM2进程信息获取失败"
    fi
    
    # 应用进程
    APP_PID=$(pgrep -f "node.*server.js" | head -1)
    if [[ -n "$APP_PID" ]]; then
        echo "应用进程: PID $APP_PID"
        
        # 进程资源使用
        PS_INFO=$(ps -p $APP_PID -o pid,ppid,%cpu,%mem,vsz,rss,tty,stat,start,time,cmd --no-headers 2>/dev/null)
        if [[ -n "$PS_INFO" ]]; then
            echo "  CPU: $(echo $PS_INFO | awk '{print $3}')%"
            echo "  内存: $(echo $PS_INFO | awk '{print $4}')% ($(echo $PS_INFO | awk '{print $6/1024}' | cut -d. -f1)MB)"
            echo "  启动时间: $(echo $PS_INFO | awk '{print $9}')"
        fi
    else
        echo -e "${RED}应用进程未运行${NC}"
    fi
    
    # 服务状态
    if systemctl is-active --quiet nginx; then
        echo -e "Nginx状态: ${GREEN}运行中${NC}"
    else
        echo -e "Nginx状态: ${RED}未运行${NC}"
    fi
    echo ""
}

# 获取应用统计信息
get_app_stats() {
    echo -e "${BLUE}=== 应用统计信息 ===${NC}"
    
    # 尝试获取应用统计
    STATS=$(curl -s --connect-timeout 5 http://localhost:8080/stats 2>/dev/null)
    if [[ $? -eq 0 && -n "$STATS" ]]; then
        echo "$STATS" | jq -r '
            "总连接数: \(.totalConnections)",
            "活跃连接数: \(.activeConnections)", 
            "总房间数: \(.totalRooms)",
            "处理消息数: \(.messagesProcessed)",
            "运行时间: \(.uptime/1000/60|floor)分钟",
            "内存使用: RSS=\(.memoryUsage.rss/1024/1024|floor)MB, Heap=\(.memoryUsage.heapUsed/1024/1024|floor)MB"
        ' 2>/dev/null || echo "统计信息解析失败"
    else
        echo -e "${YELLOW}无法获取应用统计信息${NC}"
    fi
    echo ""
}

# 检查日志错误
check_logs() {
    echo -e "${BLUE}=== 最近日志 ===${NC}"
    
    # PM2日志
    if command -v pm2 &> /dev/null; then
        echo "最近的错误日志:"
        pm2 logs $APP_NAME --lines 5 --nostream 2>/dev/null | grep -i error | tail -3 || echo "  无错误日志"
    fi
    
    # 系统日志
    echo "系统日志 (最近5条):"
    journalctl -u $APP_NAME --lines 5 --no-pager -q 2>/dev/null | tail -5 || echo "  无系统日志"
    echo ""
}

# 性能建议
performance_suggestions() {
    echo -e "${BLUE}=== 性能建议 ===${NC}"
    
    # 内存建议
    MEMORY_PERCENT=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    if (( $(echo "$MEMORY_PERCENT > 80" | bc -l) )); then
        echo -e "${YELLOW}• 内存使用率较高，建议检查内存泄漏或增加内存${NC}"
    fi
    
    # CPU建议
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
        echo -e "${YELLOW}• CPU使用率较高，建议优化代码或升级CPU${NC}"
    fi
    
    # 连接数建议
    if [[ $ACTIVE_CONNECTIONS -gt 100 ]]; then
        echo -e "${YELLOW}• 连接数较多，建议监控连接池和内存使用${NC}"
    fi
    
    # 磁盘建议
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    if [[ $DISK_USAGE -gt 80 ]]; then
        echo -e "${YELLOW}• 磁盘使用率较高，建议清理日志或扩容${NC}"
    fi
    
    echo ""
}

# 主函数
main() {
    clear
    echo -e "${CYAN}端到端加密聊天服务器 - 性能监控${NC}"
    echo "监控时间: $(date)"
    echo "========================================"
    echo ""
    
    get_system_info
    get_resource_usage
    get_network_info
    get_app_status
    get_app_stats
    check_logs
    performance_suggestions
    
    # 记录到日志文件
    if [[ -w "$(dirname "$LOG_FILE")" ]]; then
        {
            echo "=== $(date) ==="
            echo "CPU: $CPU_USAGE%, 内存: $MEMORY_PERCENT%, 连接: $ACTIVE_CONNECTIONS"
            echo ""
        } >> "$LOG_FILE"
    fi
}

# 持续监控模式
continuous_monitor() {
    while true; do
        main
        echo -e "${CYAN}按 Ctrl+C 退出，或等待30秒自动刷新...${NC}"
        sleep 30
    done
}

# 脚本入口
case "${1:-}" in
    "continuous"|"-c")
        continuous_monitor
        ;;
    "help"|"-h")
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  无参数    - 显示一次性监控信息"
        echo "  -c        - 持续监控模式"
        echo "  -h        - 显示帮助信息"
        ;;
    *)
        main
        ;;
esac
