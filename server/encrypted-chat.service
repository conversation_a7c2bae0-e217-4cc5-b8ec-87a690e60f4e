[Unit]
Description=Encrypted Chat Server - 端到端加密聊天服务器
Documentation=https://github.com/your-repo/encrypted-chat
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/encrypted-chat/server
ExecStart=/usr/bin/node server.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=8080
Environment=HOST=127.0.0.1
Environment=MAX_ROOMS=500
Environment=MAX_CLIENTS_PER_ROOM=8
Environment=ROOM_CLEANUP_INTERVAL=180000
Environment=CLIENT_TIMEOUT=300000
Environment=HEARTBEAT_INTERVAL=45000
Environment=MAX_MESSAGE_SIZE=32768
Environment=ENABLE_CORS=true

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/encrypted-chat /var/log/encrypted-chat
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制（针对低性能服务器）
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=256M
CPUQuota=80%

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=encrypted-chat

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
