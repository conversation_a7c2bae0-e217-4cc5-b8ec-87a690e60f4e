{"name": "encrypted-chat-server", "version": "1.0.0", "description": "轻量化端到端加密聊天服务器", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node --inspect server.js", "test": "node test.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop encrypted-chat", "pm2:restart": "pm2 restart encrypted-chat", "pm2:logs": "pm2 logs encrypted-chat"}, "keywords": ["websocket", "chat", "encryption", "lightweight", "ubuntu"], "author": "Your Name", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "devDependencies": {}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "homepage": "."}