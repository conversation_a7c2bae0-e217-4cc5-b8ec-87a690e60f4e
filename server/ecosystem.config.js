/**
 * PM2 生态系统配置
 * 专为Ubuntu 22.04 LTS和低性能服务器优化
 */

module.exports = {
  apps: [{
    // 应用基本信息
    name: 'encrypted-chat',
    script: 'server.js',
    cwd: '/path/to/your/server', // 需要修改为实际路径
    
    // 实例配置（针对低性能服务器）
    instances: 1, // 单实例，避免资源竞争
    exec_mode: 'fork', // fork模式，内存占用更少
    
    // 性能优化
    max_memory_restart: '200M', // 内存超过200MB时重启
    min_uptime: '10s', // 最小运行时间
    max_restarts: 10, // 最大重启次数
    restart_delay: 4000, // 重启延迟
    
    // 环境变量
    env: {
      NODE_ENV: 'production',
      PORT: 8080,
      HOST: '0.0.0.0',
      MAX_ROOMS: 500, // 降低房间数限制
      MAX_CLIENTS_PER_ROOM: 8, // 降低每房间人数
      ROOM_CLEANUP_INTERVAL: 180000, // 3分钟清理一次
      CLIENT_TIMEOUT: 300000, // 5分钟超时
      HEARTBEAT_INTERVAL: 45000, // 45秒心跳
      MAX_MESSAGE_SIZE: 32768, // 32KB消息限制
      ENABLE_CORS: true
    },
    
    // 开发环境
    env_development: {
      NODE_ENV: 'development',
      PORT: 8080,
      HOST: '127.0.0.1',
      MAX_ROOMS: 100,
      MAX_CLIENTS_PER_ROOM: 4,
      ROOM_CLEANUP_INTERVAL: 60000,
      CLIENT_TIMEOUT: 120000,
      HEARTBEAT_INTERVAL: 30000,
      MAX_MESSAGE_SIZE: 65536,
      ENABLE_CORS: true
    },
    
    // 日志配置
    log_file: '/var/log/encrypted-chat/combined.log',
    out_file: '/var/log/encrypted-chat/out.log',
    error_file: '/var/log/encrypted-chat/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 进程监控
    watch: false, // 生产环境不启用文件监控
    ignore_watch: ['node_modules', 'logs', '*.log'],
    
    // 自动重启配置
    autorestart: true,
    
    // Node.js参数优化（针对低内存服务器）
    node_args: [
      '--max-old-space-size=128', // 限制V8堆内存为128MB
      '--gc-interval=100', // 更频繁的垃圾回收
      '--optimize-for-size' // 优化内存使用而非速度
    ],
    
    // 健康检查
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true
  }],

  // 部署配置
  deploy: {
    production: {
      user: 'ubuntu', // Ubuntu默认用户
      host: ['your-server-ip'], // 替换为实际服务器IP
      ref: 'origin/main',
      repo: 'your-git-repo', // 替换为实际仓库地址
      path: '/home/<USER>/encrypted-chat',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'sudo apt update && sudo apt install -y nodejs npm'
    }
  }
};
