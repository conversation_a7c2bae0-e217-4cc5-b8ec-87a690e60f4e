#!/usr/bin/env node

/**
 * 服务器功能测试
 * 测试WebSocket连接、房间管理、消息转发等功能
 */

const WebSocket = require('ws');

// 测试配置
const TEST_CONFIG = {
    SERVER_URL: 'ws://localhost:8080',
    TEST_ROOM_ID: 'TEST123',
    TEST_MESSAGE: 'Hello, encrypted world! 你好，加密世界！',
    TEST_TIMEOUT: 10000
};

// 测试结果
let testResults = {
    passed: 0,
    failed: 0,
    total: 0
};

// 测试工具函数
function assert(condition, message) {
    testResults.total++;
    if (condition) {
        console.log(`✅ PASS: ${message}`);
        testResults.passed++;
    } else {
        console.log(`❌ FAIL: ${message}`);
        testResults.failed++;
    }
}

function createTestClient(clientName) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(TEST_CONFIG.SERVER_URL);
        const client = {
            ws,
            name: clientName,
            messages: [],
            connected: false
        };

        ws.on('open', () => {
            client.connected = true;
            console.log(`[${clientName}] 连接成功`);
            // 给服务器一点时间处理连接
            setTimeout(() => resolve(client), 100);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                client.messages.push(message);
                console.log(`[${clientName}] 收到消息: ${message.type}`, message.type === 'error' ? `- ${message.message}` : '');
            } catch (error) {
                console.error(`[${clientName}] 消息解析错误:`, error);
            }
        });

        ws.on('error', (error) => {
            console.error(`[${clientName}] 连接错误:`, error.message);
            reject(error);
        });

        ws.on('close', () => {
            client.connected = false;
            console.log(`[${clientName}] 连接关闭`);
        });

        // 超时处理
        setTimeout(() => {
            if (!client.connected) {
                reject(new Error(`${clientName} 连接超时`));
            }
        }, 5000);
    });
}

function sendMessage(client, message) {
    if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify(message));
        return true;
    }
    return false;
}

function waitForMessage(client, messageType, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        const checkMessage = () => {
            const message = client.messages.find(msg => msg.type === messageType);
            if (message) {
                resolve(message);
                return;
            }
            
            if (Date.now() - startTime > timeout) {
                reject(new Error(`等待消息 ${messageType} 超时`));
                return;
            }
            
            setTimeout(checkMessage, 100);
        };
        
        checkMessage();
    });
}

// 测试函数
async function testServerConnection() {
    console.log('\n=== 测试服务器连接 ===');
    
    try {
        const client = await createTestClient('TestClient');
        assert(client.connected, '服务器连接成功');
        client.ws.close();
    } catch (error) {
        assert(false, `服务器连接失败: ${error.message}`);
    }
}

async function testRoomJoin() {
    console.log('\n=== 测试房间加入 ===');

    try {
        const client = await createTestClient('RoomTestClient');

        // 立即发送加入房间消息
        const joinMessage = {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        };

        console.log(`[Test] 发送加入房间消息: ${TEST_CONFIG.TEST_ROOM_ID}`);
        const sent = sendMessage(client, joinMessage);
        console.log(`[Test] 消息发送状态: ${sent}`);

        // 等待房间加入确认
        const response = await waitForMessage(client, 'room_joined');
        assert(response.roomId === TEST_CONFIG.TEST_ROOM_ID, '房间加入成功');
        assert(response.peerCount === 0, '房间人数统计正确');

        client.ws.close();
    } catch (error) {
        assert(false, `房间加入测试失败: ${error.message}`);
    }
}

async function testMultipleClients() {
    console.log('\n=== 测试多客户端通信 ===');
    
    try {
        // 创建两个客户端
        const client1 = await createTestClient('Client1');
        const client2 = await createTestClient('Client2');
        
        // 客户端1加入房间
        sendMessage(client1, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        await waitForMessage(client1, 'room_joined');
        
        // 客户端2加入房间
        sendMessage(client2, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        await waitForMessage(client2, 'room_joined');
        
        // 检查客户端1是否收到新用户加入通知
        const peerJoinedMsg = await waitForMessage(client1, 'peer_joined');
        assert(peerJoinedMsg.peerCount === 2, '新用户加入通知正确');
        
        client1.ws.close();
        client2.ws.close();
    } catch (error) {
        assert(false, `多客户端测试失败: ${error.message}`);
    }
}

async function testKeyExchange() {
    console.log('\n=== 测试密钥交换 ===');
    
    try {
        const client1 = await createTestClient('KeyClient1');
        const client2 = await createTestClient('KeyClient2');
        
        // 两个客户端都加入房间
        sendMessage(client1, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        sendMessage(client2, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        await waitForMessage(client1, 'room_joined');
        await waitForMessage(client2, 'room_joined');
        
        // 客户端1发送密钥交换
        const fakePublicKey = 'fake_public_key_data_for_testing';
        sendMessage(client1, {
            type: 'key_exchange',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            publicKey: fakePublicKey,
            timestamp: Date.now()
        });
        
        // 检查客户端2是否收到密钥交换消息
        const keyExchangeMsg = await waitForMessage(client2, 'key_exchange');
        assert(keyExchangeMsg.publicKey === fakePublicKey, '密钥交换消息转发正确');
        
        client1.ws.close();
        client2.ws.close();
    } catch (error) {
        assert(false, `密钥交换测试失败: ${error.message}`);
    }
}

async function testEncryptedMessage() {
    console.log('\n=== 测试加密消息转发 ===');
    
    try {
        const client1 = await createTestClient('MsgClient1');
        const client2 = await createTestClient('MsgClient2');
        
        // 两个客户端都加入房间
        sendMessage(client1, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        sendMessage(client2, {
            type: 'join_room',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            timestamp: Date.now()
        });
        
        await waitForMessage(client1, 'room_joined');
        await waitForMessage(client2, 'room_joined');
        
        // 客户端1发送加密消息
        const fakeEncryptedData = 'fake_encrypted_data_for_testing';
        const fakeIV = 'fake_iv_for_testing';
        
        sendMessage(client1, {
            type: 'encrypted_message',
            roomId: TEST_CONFIG.TEST_ROOM_ID,
            encryptedData: fakeEncryptedData,
            iv: fakeIV,
            timestamp: Date.now()
        });
        
        // 检查客户端2是否收到加密消息
        const encryptedMsg = await waitForMessage(client2, 'encrypted_message');
        assert(encryptedMsg.encryptedData === fakeEncryptedData, '加密消息数据转发正确');
        assert(encryptedMsg.iv === fakeIV, '加密消息IV转发正确');
        
        client1.ws.close();
        client2.ws.close();
    } catch (error) {
        assert(false, `加密消息测试失败: ${error.message}`);
    }
}

async function testServerStats() {
    console.log('\n=== 测试服务器统计 ===');
    
    try {
        const http = require('http');
        
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/stats',
            method: 'GET'
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const stats = JSON.parse(data);
                    assert(typeof stats.totalConnections === 'number', '统计信息包含总连接数');
                    assert(typeof stats.activeConnections === 'number', '统计信息包含活跃连接数');
                    assert(typeof stats.uptime === 'number', '统计信息包含运行时间');
                    console.log('服务器统计信息:', stats);
                } catch (error) {
                    assert(false, `统计信息解析失败: ${error.message}`);
                }
            });
        });
        
        req.on('error', (error) => {
            assert(false, `获取统计信息失败: ${error.message}`);
        });
        
        req.end();
    } catch (error) {
        assert(false, `服务器统计测试失败: ${error.message}`);
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始服务器功能测试...\n');
    
    try {
        await testServerConnection();
        await testRoomJoin();
        await testMultipleClients();
        await testKeyExchange();
        await testEncryptedMessage();
        await testServerStats();
        
        // 等待异步操作完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
    
    // 输出测试结果
    console.log('\n=== 测试结果 ===');
    console.log(`总测试数: ${testResults.total}`);
    console.log(`通过: ${testResults.passed}`);
    console.log(`失败: ${testResults.failed}`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    if (testResults.failed === 0) {
        console.log('🎉 所有测试通过！');
        process.exit(0);
    } else {
        console.log('❌ 部分测试失败');
        process.exit(1);
    }
}

// 检查服务器是否运行
function checkServerRunning() {
    const client = new WebSocket(TEST_CONFIG.SERVER_URL);
    
    client.on('open', () => {
        client.close();
        runAllTests();
    });
    
    client.on('error', () => {
        console.error('❌ 服务器未运行，请先启动服务器: npm start');
        process.exit(1);
    });
}

// 启动测试
if (require.main === module) {
    checkServerRunning();
}

module.exports = { runAllTests, testResults };
