#!/usr/bin/env node

/**
 * 轻量化端到端加密聊天服务器
 * 专为Ubuntu 22.04 LTS和低性能服务器优化
 * 
 * 特性：
 * - 纯内存存储，无数据库依赖
 * - 最小化内存占用
 * - 高效的消息转发
 * - 自动清理机制
 * - 进程监控和重启
 */

const WebSocket = require('ws');
const http = require('http');
const url = require('url');

// 服务器配置
const CONFIG = {
    PORT: process.env.PORT || 8080,
    HOST: process.env.HOST || '0.0.0.0',
    MAX_ROOMS: parseInt(process.env.MAX_ROOMS) || 1000,
    MAX_CLIENTS_PER_ROOM: parseInt(process.env.MAX_CLIENTS_PER_ROOM) || 10,
    ROOM_CLEANUP_INTERVAL: parseInt(process.env.ROOM_CLEANUP_INTERVAL) || 300000, // 5分钟
    CLIENT_TIMEOUT: parseInt(process.env.CLIENT_TIMEOUT) || 600000, // 10分钟
    HEARTBEAT_INTERVAL: parseInt(process.env.HEARTBEAT_INTERVAL) || 30000, // 30秒
    MAX_MESSAGE_SIZE: parseInt(process.env.MAX_MESSAGE_SIZE) || 64 * 1024, // 64KB
    ENABLE_CORS: process.env.ENABLE_CORS !== 'false'
};

// 全局状态管理（内存优化）
class ServerState {
    constructor() {
        this.rooms = new Map(); // roomId -> Set<WebSocket>
        this.clients = new Map(); // WebSocket -> { roomId, lastSeen, id }
        this.stats = {
            totalConnections: 0,
            activeConnections: 0,
            totalRooms: 0,
            messagesProcessed: 0,
            startTime: Date.now()
        };
    }

    // 添加客户端到房间
    addClientToRoom(ws, roomId) {
        // 检查房间数量限制
        if (this.rooms.size >= CONFIG.MAX_ROOMS) {
            throw new Error('服务器房间数量已达上限');
        }

        // 检查房间客户端数量限制
        if (!this.rooms.has(roomId)) {
            this.rooms.set(roomId, new Set());
            this.stats.totalRooms++;
        }

        const room = this.rooms.get(roomId);
        if (room.size >= CONFIG.MAX_CLIENTS_PER_ROOM) {
            throw new Error('房间人数已满');
        }

        // 添加客户端
        room.add(ws);
        this.clients.set(ws, {
            roomId,
            lastSeen: Date.now(),
            id: this.generateClientId()
        });

        this.stats.activeConnections++;
        console.log(`[Server] 客户端加入房间 ${roomId}, 当前房间人数: ${room.size}`);
        
        return room.size;
    }

    // 从房间移除客户端
    removeClientFromRoom(ws) {
        const clientInfo = this.clients.get(ws);
        if (!clientInfo) return;

        const { roomId } = clientInfo;
        const room = this.rooms.get(roomId);
        
        if (room) {
            room.delete(ws);
            console.log(`[Server] 客户端离开房间 ${roomId}, 剩余人数: ${room.size}`);
            
            // 如果房间为空，删除房间
            if (room.size === 0) {
                this.rooms.delete(roomId);
                this.stats.totalRooms--;
                console.log(`[Server] 房间 ${roomId} 已清空并删除`);
            }
        }

        this.clients.delete(ws);
        this.stats.activeConnections--;
    }

    // 获取房间内的其他客户端
    getRoomClients(roomId, excludeWs = null) {
        const room = this.rooms.get(roomId);
        if (!room) return [];
        
        return Array.from(room).filter(ws => ws !== excludeWs && ws.readyState === WebSocket.OPEN);
    }

    // 更新客户端最后活跃时间
    updateClientActivity(ws) {
        const clientInfo = this.clients.get(ws);
        if (clientInfo) {
            clientInfo.lastSeen = Date.now();
        }
    }

    // 生成客户端ID
    generateClientId() {
        return Math.random().toString(36).substring(2, 10);
    }

    // 清理超时客户端
    cleanupTimeoutClients() {
        const now = Date.now();
        const timeoutClients = [];

        for (const [ws, clientInfo] of this.clients) {
            if (now - clientInfo.lastSeen > CONFIG.CLIENT_TIMEOUT) {
                timeoutClients.push(ws);
            }
        }

        timeoutClients.forEach(ws => {
            console.log(`[Server] 清理超时客户端`);
            ws.terminate();
            this.removeClientFromRoom(ws);
        });

        return timeoutClients.length;
    }

    // 获取服务器统计信息
    getStats() {
        return {
            ...this.stats,
            uptime: Date.now() - this.stats.startTime,
            memoryUsage: process.memoryUsage()
        };
    }
}

// 创建服务器状态实例
const serverState = new ServerState();

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 处理CORS
    if (CONFIG.ENABLE_CORS) {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    }

    const parsedUrl = url.parse(req.url, true);
    
    // 健康检查端点
    if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'healthy',
            ...serverState.getStats()
        }));
        return;
    }

    // 统计信息端点
    if (parsedUrl.pathname === '/stats') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(serverState.getStats()));
        return;
    }

    // 默认响应
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({
    server,
    maxPayload: CONFIG.MAX_MESSAGE_SIZE,
    perMessageDeflate: false, // 禁用压缩以节省CPU
    clientTracking: false // 禁用内置客户端跟踪，使用自定义实现
});

// WebSocket连接处理
wss.on('connection', (ws, req) => {
    serverState.stats.totalConnections++;
    console.log(`[Server] 新连接建立, 总连接数: ${serverState.stats.totalConnections}`);

    // 连接超时处理
    const connectionTimeout = setTimeout(() => {
        console.log('[Server] 连接超时，关闭连接');
        ws.terminate();
    }, 10000); // 10秒内必须加入房间

    // 消息处理
    ws.on('message', (data) => {
        try {
            // 清除连接超时
            clearTimeout(connectionTimeout);
            
            // 更新客户端活跃时间
            serverState.updateClientActivity(ws);
            
            // 解析消息
            const message = JSON.parse(data.toString());
            handleMessage(ws, message);
            
            serverState.stats.messagesProcessed++;
            
        } catch (error) {
            console.error('[Server] 消息处理错误:', error.message);
            sendError(ws, '消息格式错误');
        }
    });

    // 连接关闭处理
    ws.on('close', () => {
        clearTimeout(connectionTimeout);
        serverState.removeClientFromRoom(ws);
        console.log(`[Server] 连接关闭, 活跃连接数: ${serverState.stats.activeConnections}`);
    });

    // 错误处理
    ws.on('error', (error) => {
        console.error('[Server] WebSocket错误:', error.message);
        clearTimeout(connectionTimeout);
        serverState.removeClientFromRoom(ws);
    });

    // 心跳检测
    ws.isAlive = true;
    ws.on('pong', () => {
        ws.isAlive = true;
        serverState.updateClientActivity(ws);
    });
});

// 消息处理函数
function handleMessage(ws, message) {
    const { type, roomId } = message;

    switch (type) {
        case 'join_room':
            handleJoinRoom(ws, message);
            break;
            
        case 'key_exchange':
            handleKeyExchange(ws, message);
            break;
            
        case 'encrypted_message':
            handleEncryptedMessage(ws, message);
            break;
            
        default:
            console.warn(`[Server] 未知消息类型: ${type}`);
            sendError(ws, '未知消息类型');
    }
}

// 处理加入房间
function handleJoinRoom(ws, message) {
    const { roomId } = message;
    
    if (!roomId || typeof roomId !== 'string' || roomId.length > 20) {
        sendError(ws, '无效的房间ID');
        return;
    }

    try {
        const roomSize = serverState.addClientToRoom(ws, roomId);
        
        // 发送加入成功消息
        sendMessage(ws, {
            type: 'room_joined',
            roomId,
            peerCount: roomSize - 1
        });

        // 通知房间内其他用户
        const otherClients = serverState.getRoomClients(roomId, ws);
        otherClients.forEach(client => {
            sendMessage(client, {
                type: 'peer_joined',
                roomId,
                peerCount: roomSize
            });
        });

    } catch (error) {
        sendError(ws, error.message);
    }
}

// 处理密钥交换
function handleKeyExchange(ws, message) {
    const clientInfo = serverState.clients.get(ws);
    if (!clientInfo) {
        sendError(ws, '未加入房间');
        return;
    }

    const { roomId, publicKey } = message;
    
    // 转发给房间内其他用户
    const otherClients = serverState.getRoomClients(roomId, ws);
    otherClients.forEach(client => {
        sendMessage(client, {
            type: 'key_exchange',
            roomId,
            publicKey,
            timestamp: message.timestamp
        });
    });
}

// 处理加密消息
function handleEncryptedMessage(ws, message) {
    const clientInfo = serverState.clients.get(ws);
    if (!clientInfo) {
        sendError(ws, '未加入房间');
        return;
    }

    const { roomId, encryptedData, iv } = message;
    
    // 转发给房间内其他用户
    const otherClients = serverState.getRoomClients(roomId, ws);
    otherClients.forEach(client => {
        sendMessage(client, {
            type: 'encrypted_message',
            roomId,
            encryptedData,
            iv,
            timestamp: message.timestamp
        });
    });
}

// 发送消息
function sendMessage(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
        try {
            ws.send(JSON.stringify(message));
        } catch (error) {
            console.error('[Server] 发送消息失败:', error.message);
        }
    }
}

// 发送错误消息
function sendError(ws, errorMessage) {
    sendMessage(ws, {
        type: 'error',
        message: errorMessage,
        timestamp: Date.now()
    });
}

// 心跳检测
const heartbeatInterval = setInterval(() => {
    // 使用自定义客户端跟踪而不是wss.clients
    for (const [ws, clientInfo] of serverState.clients) {
        if (ws.readyState === WebSocket.OPEN) {
            if (ws.isAlive === false) {
                console.log('[Server] 心跳检测失败，终止连接');
                ws.terminate();
                serverState.removeClientFromRoom(ws);
                continue;
            }

            ws.isAlive = false;
            ws.ping();
        } else {
            // 清理已关闭的连接
            serverState.removeClientFromRoom(ws);
        }
    }
}, CONFIG.HEARTBEAT_INTERVAL);

// 定期清理
const cleanupInterval = setInterval(() => {
    const cleanedCount = serverState.cleanupTimeoutClients();
    if (cleanedCount > 0) {
        console.log(`[Server] 清理了 ${cleanedCount} 个超时客户端`);
    }
    
    // 强制垃圾回收（如果可用）
    if (global.gc) {
        global.gc();
    }
}, CONFIG.ROOM_CLEANUP_INTERVAL);

// 优雅关闭
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

function gracefulShutdown() {
    console.log('[Server] 开始优雅关闭...');

    clearInterval(heartbeatInterval);
    clearInterval(cleanupInterval);

    // 关闭所有客户端连接
    for (const [ws, clientInfo] of serverState.clients) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.close(1000, '服务器关闭');
        }
    }

    server.close(() => {
        console.log('[Server] 服务器已关闭');
        process.exit(0);
    });
}

// 启动服务器
server.listen(CONFIG.PORT, CONFIG.HOST, () => {
    console.log(`[Server] 服务器启动成功`);
    console.log(`[Server] 监听地址: ${CONFIG.HOST}:${CONFIG.PORT}`);
    console.log(`[Server] 最大房间数: ${CONFIG.MAX_ROOMS}`);
    console.log(`[Server] 每房间最大人数: ${CONFIG.MAX_CLIENTS_PER_ROOM}`);
    console.log(`[Server] 进程ID: ${process.pid}`);
    console.log(`[Server] Node.js版本: ${process.version}`);
    console.log(`[Server] 内存使用: ${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`);
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('[Server] 未捕获的异常:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('[Server] 未处理的Promise拒绝:', reason);
});

module.exports = { server, wss, serverState, CONFIG };
