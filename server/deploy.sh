#!/bin/bash

# 端到端加密聊天服务器部署脚本
# 专为Ubuntu 22.04 LTS优化

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="encrypted-chat"
APP_USER="ubuntu"
APP_DIR="/home/<USER>/encrypted-chat"
SERVER_DIR="$APP_DIR/server"
LOG_DIR="/var/log/encrypted-chat"
SERVICE_NAME="encrypted-chat"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    source /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]]; then
        log_warn "此脚本专为Ubuntu设计，当前系统: $ID"
    fi
    
    if [[ "$VERSION_ID" != "22.04" ]]; then
        log_warn "此脚本专为Ubuntu 22.04设计，当前版本: $VERSION_ID"
    fi
    
    log_info "系统检查完成: $PRETTY_NAME"
}

# 安装系统依赖
install_dependencies() {
    log_step "安装系统依赖..."
    
    # 更新包列表
    sudo apt update
    
    # 安装必要的包
    sudo apt install -y \
        curl \
        wget \
        git \
        build-essential \
        nginx \
        ufw \
        htop \
        unzip
    
    log_info "系统依赖安装完成"
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js..."
    
    # 检查是否已安装Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        
        # 检查版本是否满足要求
        if [[ "$NODE_VERSION" < "v16.0.0" ]]; then
            log_warn "Node.js版本过低，建议升级到v16+"
        fi
    else
        # 安装Node.js 18 LTS
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
        
        log_info "Node.js安装完成: $(node --version)"
    fi
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        sudo npm install -g pm2
        log_info "PM2安装完成"
    else
        log_info "PM2已安装: $(pm2 --version)"
    fi
}

# 创建应用目录
setup_directories() {
    log_step "创建应用目录..."
    
    # 创建应用目录
    mkdir -p "$APP_DIR"
    mkdir -p "$SERVER_DIR"
    
    # 创建日志目录
    sudo mkdir -p "$LOG_DIR"
    sudo chown "$APP_USER:$APP_USER" "$LOG_DIR"
    
    log_info "目录创建完成"
}

# 部署应用代码
deploy_application() {
    log_step "部署应用代码..."
    
    # 复制服务器文件
    if [[ -f "server.js" ]]; then
        cp server.js "$SERVER_DIR/"
        cp package.json "$SERVER_DIR/"
        cp ecosystem.config.js "$SERVER_DIR/"
        cp test.js "$SERVER_DIR/"
        
        log_info "服务器文件复制完成"
    else
        log_error "找不到服务器文件，请确保在正确的目录中运行脚本"
        exit 1
    fi
    
    # 安装依赖
    cd "$SERVER_DIR"
    npm install --production
    
    log_info "应用依赖安装完成"
}

# 配置Nginx
setup_nginx() {
    log_step "配置Nginx..."
    
    # 创建Nginx配置
    sudo tee /etc/nginx/sites-available/$APP_NAME > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    # 静态文件服务
    location / {
        root $APP_DIR;
        index index.html;
        try_files \$uri \$uri/ =404;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # WebSocket代理
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
    
    # API代理
    location /api {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        access_log off;
    }
    
    # 统计信息（可选，生产环境建议移除或加密码保护）
    location /stats {
        proxy_pass http://127.0.0.1:8080/stats;
        # allow 127.0.0.1;
        # deny all;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 隐藏Nginx版本
    server_tokens off;
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试Nginx配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log_info "Nginx配置完成"
}

# 配置防火墙
setup_firewall() {
    log_step "配置防火墙..."
    
    # 启用UFW
    sudo ufw --force enable
    
    # 允许SSH
    sudo ufw allow ssh
    
    # 允许HTTP和HTTPS
    sudo ufw allow 'Nginx Full'
    
    # 显示防火墙状态
    sudo ufw status
    
    log_info "防火墙配置完成"
}

# 启动应用
start_application() {
    log_step "启动应用..."
    
    cd "$SERVER_DIR"
    
    # 更新PM2生态系统配置中的路径
    sed -i "s|/path/to/your/server|$SERVER_DIR|g" ecosystem.config.js
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    # 设置PM2开机自启
    pm2 startup
    
    log_info "应用启动完成"
}

# 运行测试
run_tests() {
    log_step "运行功能测试..."
    
    cd "$SERVER_DIR"
    
    # 等待服务启动
    sleep 5
    
    # 运行测试
    if node test.js; then
        log_info "所有测试通过"
    else
        log_warn "部分测试失败，请检查日志"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成！"
    
    echo ""
    echo "=== 部署信息 ==="
    echo "应用名称: $APP_NAME"
    echo "应用目录: $APP_DIR"
    echo "日志目录: $LOG_DIR"
    echo "服务状态: $(pm2 list | grep $APP_NAME || echo '未运行')"
    echo ""
    echo "=== 常用命令 ==="
    echo "查看应用状态: pm2 status"
    echo "查看日志: pm2 logs $APP_NAME"
    echo "重启应用: pm2 restart $APP_NAME"
    echo "停止应用: pm2 stop $APP_NAME"
    echo "查看Nginx状态: sudo systemctl status nginx"
    echo "查看服务器统计: curl http://localhost/stats"
    echo ""
    echo "=== 访问地址 ==="
    echo "Web界面: http://$(curl -s ifconfig.me || echo 'YOUR_SERVER_IP')"
    echo "WebSocket: ws://$(curl -s ifconfig.me || echo 'YOUR_SERVER_IP')/ws"
    echo ""
}

# 主函数
main() {
    log_info "开始部署端到端加密聊天服务器..."
    
    check_root
    check_system
    install_dependencies
    install_nodejs
    setup_directories
    deploy_application
    setup_nginx
    setup_firewall
    start_application
    run_tests
    show_deployment_info
    
    log_info "部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
