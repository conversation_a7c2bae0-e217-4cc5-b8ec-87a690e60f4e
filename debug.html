<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双用户调试页面</title>
    <style>
        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            margin: 0;
            padding: 20px;
            background: white;
            color: black;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 100vh;
        }
        .user-panel {
            border: 2px solid black;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .user-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            padding: 10px;
            border: 1px solid black;
            background: black;
            color: white;
        }
        .status {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid black;
            background: #f5f5f5;
        }
        .messages {
            flex: 1;
            border: 1px solid black;
            padding: 10px;
            overflow-y: auto;
            margin-bottom: 20px;
            min-height: 200px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-left: 3px solid black;
            padding-left: 10px;
        }
        .message.own {
            border-left-color: black;
            background: #f0f0f0;
        }
        .message.other {
            border-left-color: #666;
            background: white;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        input, button {
            font-family: inherit;
            padding: 10px;
            border: 1px solid black;
        }
        input {
            flex: 1;
        }
        button {
            background: white;
            cursor: pointer;
        }
        button:hover {
            background: black;
            color: white;
        }
        .controls {
            margin-bottom: 20px;
        }
        .log {
            font-size: 12px;
            color: #666;
            max-height: 100px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>端到端加密聊天 - 双用户调试</h1>
    
    <div class="container">
        <!-- 用户1 -->
        <div class="user-panel">
            <div class="user-title">用户 1</div>
            
            <div class="controls">
                <input type="text" id="roomId1" placeholder="房间ID" value="DEBUG123">
                <button onclick="connectUser1()">连接</button>
                <button onclick="disconnectUser1()">断开</button>
            </div>
            
            <div class="status" id="status1">
                连接状态: <span id="conn1">未连接</span><br>
                加密状态: <span id="enc1">未加密</span><br>
                用户数量: <span id="peers1">0</span>
            </div>
            
            <div class="messages" id="messages1"></div>
            
            <div class="input-area">
                <input type="text" id="input1" placeholder="输入消息..." onkeypress="if(event.key==='Enter') sendMessage1()">
                <button onclick="sendMessage1()">发送</button>
            </div>
            
            <div class="log" id="log1"></div>
        </div>
        
        <!-- 用户2 -->
        <div class="user-panel">
            <div class="user-title">用户 2</div>
            
            <div class="controls">
                <input type="text" id="roomId2" placeholder="房间ID" value="DEBUG123">
                <button onclick="connectUser2()">连接</button>
                <button onclick="disconnectUser2()">断开</button>
            </div>
            
            <div class="status" id="status2">
                连接状态: <span id="conn2">未连接</span><br>
                加密状态: <span id="enc2">未加密</span><br>
                用户数量: <span id="peers2">0</span>
            </div>
            
            <div class="messages" id="messages2"></div>
            
            <div class="input-area">
                <input type="text" id="input2" placeholder="输入消息..." onkeypress="if(event.key==='Enter') sendMessage2()">
                <button onclick="sendMessage2()">发送</button>
            </div>
            
            <div class="log" id="log2"></div>
        </div>
    </div>

    <script src="scripts/crypto.js"></script>
    <script src="scripts/websocket.js"></script>
    
    <script>
        // 用户1的实例
        let crypto1, ws1, app1;
        // 用户2的实例
        let crypto2, ws2, app2;
        
        // 初始化用户1
        async function initUser1() {
            crypto1 = new CryptoManager();
            await crypto1.initialize();
            ws1 = new WebSocketManager();
            
            // 绑定事件
            ws1.on('connected', () => {
                document.getElementById('conn1').textContent = '已连接';
                log1('WebSocket连接成功');
            });
            
            ws1.on('disconnected', () => {
                document.getElementById('conn1').textContent = '未连接';
                document.getElementById('enc1').textContent = '未加密';
                log1('WebSocket连接断开');
            });
            
            ws1.on('room_joined', (data) => {
                log1('成功加入房间');
            });
            
            ws1.on('peer_joined', async (data) => {
                document.getElementById('peers1').textContent = data.peerCount || '1';
                log1('新用户加入');
                
                // 发送公钥
                try {
                    const publicKey = await crypto1.getPublicKey();
                    const publicKeyBase64 = arrayBufferToBase64(publicKey);
                    ws1.sendKeyExchange(publicKeyBase64);
                    log1('发送公钥');
                } catch (error) {
                    log1('发送公钥失败: ' + error.message);
                }
            });
            
            ws1.on('key_exchange', async (data) => {
                log1('收到密钥交换');
                try {
                    const peerPublicKey = base64ToArrayBuffer(data.publicKey);
                    const success = await crypto1.setPeerPublicKey(peerPublicKey);
                    if (success) {
                        document.getElementById('enc1').textContent = '已加密';
                        log1('密钥交换成功');
                    }
                } catch (error) {
                    log1('密钥交换失败: ' + error.message);
                }
            });
            
            ws1.on('encrypted_message', async (data) => {
                log1('收到加密消息');
                try {
                    const encryptedData = base64ToArrayBuffer(data.encryptedData);
                    const iv = base64ToUint8Array(data.iv);
                    const decryptedMessage = await crypto1.decryptMessage(encryptedData, iv);
                    addMessage1(decryptedMessage, false);
                    log1('消息解密成功');
                } catch (error) {
                    addMessage1('[解密失败]', false);
                    log1('解密失败: ' + error.message);
                }
            });
        }
        
        // 初始化用户2
        async function initUser2() {
            crypto2 = new CryptoManager();
            await crypto2.initialize();
            ws2 = new WebSocketManager();
            
            // 绑定事件（类似用户1）
            ws2.on('connected', () => {
                document.getElementById('conn2').textContent = '已连接';
                log2('WebSocket连接成功');
            });
            
            ws2.on('disconnected', () => {
                document.getElementById('conn2').textContent = '未连接';
                document.getElementById('enc2').textContent = '未加密';
                log2('WebSocket连接断开');
            });
            
            ws2.on('room_joined', (data) => {
                log2('成功加入房间');
            });
            
            ws2.on('peer_joined', async (data) => {
                document.getElementById('peers2').textContent = data.peerCount || '1';
                log2('新用户加入');
                
                // 发送公钥
                try {
                    const publicKey = await crypto2.getPublicKey();
                    const publicKeyBase64 = arrayBufferToBase64(publicKey);
                    ws2.sendKeyExchange(publicKeyBase64);
                    log2('发送公钥');
                } catch (error) {
                    log2('发送公钥失败: ' + error.message);
                }
            });
            
            ws2.on('key_exchange', async (data) => {
                log2('收到密钥交换');
                try {
                    const peerPublicKey = base64ToArrayBuffer(data.publicKey);
                    const success = await crypto2.setPeerPublicKey(peerPublicKey);
                    if (success) {
                        document.getElementById('enc2').textContent = '已加密';
                        log2('密钥交换成功');
                    }
                } catch (error) {
                    log2('密钥交换失败: ' + error.message);
                }
            });
            
            ws2.on('encrypted_message', async (data) => {
                log2('收到加密消息');
                try {
                    const encryptedData = base64ToArrayBuffer(data.encryptedData);
                    const iv = base64ToUint8Array(data.iv);
                    const decryptedMessage = await crypto2.decryptMessage(encryptedData, iv);
                    addMessage2(decryptedMessage, false);
                    log2('消息解密成功');
                } catch (error) {
                    addMessage2('[解密失败]', false);
                    log2('解密失败: ' + error.message);
                }
            });
        }
        
        // 连接函数
        async function connectUser1() {
            const roomId = document.getElementById('roomId1').value;
            ws1.connect('ws://localhost:8080', roomId);
        }
        
        async function connectUser2() {
            const roomId = document.getElementById('roomId2').value;
            ws2.connect('ws://localhost:8080', roomId);
        }
        
        // 断开连接
        function disconnectUser1() {
            ws1.disconnect();
        }
        
        function disconnectUser2() {
            ws2.disconnect();
        }
        
        // 发送消息
        async function sendMessage1() {
            const input = document.getElementById('input1');
            const message = input.value.trim();
            if (!message || !crypto1.isReady()) return;
            
            try {
                const { encrypted, iv } = await crypto1.encryptMessage(message);
                const encryptedBase64 = arrayBufferToBase64(encrypted);
                const ivBase64 = uint8ArrayToBase64(iv);
                
                ws1.sendEncryptedMessage(encryptedBase64, ivBase64);
                addMessage1(message, true);
                input.value = '';
                log1('消息发送成功');
            } catch (error) {
                log1('发送失败: ' + error.message);
            }
        }
        
        async function sendMessage2() {
            const input = document.getElementById('input2');
            const message = input.value.trim();
            if (!message || !crypto2.isReady()) return;
            
            try {
                const { encrypted, iv } = await crypto2.encryptMessage(message);
                const encryptedBase64 = arrayBufferToBase64(encrypted);
                const ivBase64 = uint8ArrayToBase64(iv);
                
                ws2.sendEncryptedMessage(encryptedBase64, ivBase64);
                addMessage2(message, true);
                input.value = '';
                log2('消息发送成功');
            } catch (error) {
                log2('发送失败: ' + error.message);
            }
        }
        
        // 添加消息
        function addMessage1(text, isOwn) {
            const messages = document.getElementById('messages1');
            const div = document.createElement('div');
            div.className = `message ${isOwn ? 'own' : 'other'}`;
            div.textContent = `${isOwn ? '我' : '对方'}: ${text}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function addMessage2(text, isOwn) {
            const messages = document.getElementById('messages2');
            const div = document.createElement('div');
            div.className = `message ${isOwn ? 'own' : 'other'}`;
            div.textContent = `${isOwn ? '我' : '对方'}: ${text}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        // 日志函数
        function log1(message) {
            const log = document.getElementById('log1');
            const div = document.createElement('div');
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }
        
        function log2(message) {
            const log = document.getElementById('log2');
            const div = document.createElement('div');
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initUser1();
            await initUser2();
            console.log('调试页面初始化完成');
        });
    </script>
</body>
</html>
