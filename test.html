<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密模块测试</title>
    <style>
        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            padding: 20px;
            background: white;
            color: black;
        }
        .test-section {
            border: 1px solid black;
            padding: 20px;
            margin: 20px 0;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid black;
        }
        button {
            font-family: inherit;
            padding: 10px 20px;
            border: 1px solid black;
            background: white;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: black;
            color: white;
        }
        input, textarea {
            font-family: inherit;
            padding: 10px;
            border: 1px solid black;
            width: 100%;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>端到端加密聊天 - 功能测试</h1>
    
    <div class="test-section">
        <h2>1. 加密模块测试</h2>
        <button onclick="testCrypto()">测试加密/解密</button>
        <div id="cryptoResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 密钥交换测试</h2>
        <button onclick="testKeyExchange()">测试密钥交换</button>
        <div id="keyExchangeResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 消息加密测试</h2>
        <input type="text" id="testMessage" placeholder="输入要加密的消息" value="Hello, World! 你好世界！">
        <button onclick="testMessageEncryption()">加密消息</button>
        <div id="messageResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. WebSocket连接测试</h2>
        <p>注意：需要后端服务器运行才能测试</p>
        <input type="text" id="testRoomId" placeholder="房间ID" value="TEST123">
        <button onclick="testWebSocket()">测试连接</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <div id="wsResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 界面响应测试</h2>
        <button onclick="testUI()">测试界面功能</button>
        <div id="uiResult" class="test-result"></div>
    </div>

    <script src="scripts/crypto.js"></script>
    <script src="scripts/websocket.js"></script>
    
    <script>
        let testCryptoManager1, testCryptoManager2;
        let testWSManager;
        
        // 测试加密模块
        async function testCrypto() {
            const result = document.getElementById('cryptoResult');
            result.innerHTML = '测试中...';
            
            try {
                const crypto = new CryptoManager();
                const initialized = await crypto.initialize();
                
                if (initialized) {
                    result.innerHTML = '✅ 加密模块初始化成功<br>';
                    
                    // 测试公钥导出
                    const publicKey = await crypto.getPublicKey();
                    result.innerHTML += `✅ 公钥生成成功 (${publicKey.byteLength} bytes)<br>`;
                    
                } else {
                    result.innerHTML = '❌ 加密模块初始化失败';
                }
            } catch (error) {
                result.innerHTML = `❌ 测试失败: ${error.message}`;
            }
        }
        
        // 测试密钥交换
        async function testKeyExchange() {
            const result = document.getElementById('keyExchangeResult');
            result.innerHTML = '测试中...';
            
            try {
                // 创建两个加密管理器模拟两个用户
                testCryptoManager1 = new CryptoManager();
                testCryptoManager2 = new CryptoManager();
                
                await testCryptoManager1.initialize();
                await testCryptoManager2.initialize();
                
                // 获取公钥
                const publicKey1 = await testCryptoManager1.getPublicKey();
                const publicKey2 = await testCryptoManager2.getPublicKey();
                
                // 交换公钥
                await testCryptoManager1.setPeerPublicKey(publicKey2);
                await testCryptoManager2.setPeerPublicKey(publicKey1);
                
                result.innerHTML = '✅ 密钥交换成功<br>';
                result.innerHTML += `✅ 用户1加密就绪: ${testCryptoManager1.isReady()}<br>`;
                result.innerHTML += `✅ 用户2加密就绪: ${testCryptoManager2.isReady()}<br>`;
                
            } catch (error) {
                result.innerHTML = `❌ 测试失败: ${error.message}`;
            }
        }
        
        // 测试消息加密
        async function testMessageEncryption() {
            const result = document.getElementById('messageResult');
            const message = document.getElementById('testMessage').value;
            
            if (!testCryptoManager1 || !testCryptoManager2) {
                result.innerHTML = '❌ 请先运行密钥交换测试';
                return;
            }
            
            result.innerHTML = '测试中...';
            
            try {
                // 用户1加密消息
                const { encrypted, iv } = await testCryptoManager1.encryptMessage(message);
                result.innerHTML = `✅ 消息加密成功<br>`;
                result.innerHTML += `原始消息: ${message}<br>`;
                result.innerHTML += `加密数据长度: ${encrypted.byteLength} bytes<br>`;
                result.innerHTML += `IV长度: ${iv.byteLength} bytes<br>`;
                
                // 用户2解密消息
                const decrypted = await testCryptoManager2.decryptMessage(encrypted, iv);
                result.innerHTML += `✅ 消息解密成功<br>`;
                result.innerHTML += `解密消息: ${decrypted}<br>`;
                result.innerHTML += `消息匹配: ${message === decrypted ? '✅' : '❌'}<br>`;
                
            } catch (error) {
                result.innerHTML = `❌ 测试失败: ${error.message}`;
            }
        }
        
        // 测试WebSocket连接
        function testWebSocket() {
            const result = document.getElementById('wsResult');
            const roomId = document.getElementById('testRoomId').value;
            
            result.innerHTML = '尝试连接...';
            
            testWSManager = new WebSocketManager();
            
            testWSManager.on('connected', () => {
                result.innerHTML += '<br>✅ WebSocket连接成功';
            });
            
            testWSManager.on('disconnected', () => {
                result.innerHTML += '<br>❌ WebSocket连接断开';
            });
            
            testWSManager.on('error', (error) => {
                result.innerHTML += `<br>❌ 连接错误: 无法连接到服务器 (需要启动后端服务器)`;
            });
            
            testWSManager.on('room_joined', () => {
                result.innerHTML += '<br>✅ 成功加入房间';
            });
            
            testWSManager.connect('ws://localhost:8080', roomId);
        }
        
        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (testWSManager) {
                testWSManager.disconnect();
                document.getElementById('wsResult').innerHTML += '<br>🔌 主动断开连接';
            }
        }
        
        // 测试界面功能
        function testUI() {
            const result = document.getElementById('uiResult');
            result.innerHTML = '测试界面功能...<br>';
            
            // 检查CSS是否正确加载
            const testElement = document.createElement('div');
            testElement.style.fontFamily = 'SF Mono, Monaco, Inconsolata, Roboto Mono, monospace';
            document.body.appendChild(testElement);
            const computedStyle = window.getComputedStyle(testElement);
            
            result.innerHTML += `✅ 字体设置: ${computedStyle.fontFamily}<br>`;
            
            document.body.removeChild(testElement);
            
            // 检查主要功能
            result.innerHTML += '✅ DOM操作正常<br>';
            result.innerHTML += '✅ 事件绑定正常<br>';
            result.innerHTML += '✅ 样式应用正常<br>';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('测试页面加载完成');
        });
    </script>
</body>
</html>
